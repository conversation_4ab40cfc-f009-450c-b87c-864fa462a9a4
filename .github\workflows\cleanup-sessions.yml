name: Cleanup Expired Sessions

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  cleanup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Cleanup Sessions
      run: |
        curl -X GET "${{ secrets.VERCEL_APP_URL }}/api/cron/cleanup-sessions" \
          -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
          -H "Content-Type: application/json"
      env:
        VERCEL_APP_URL: ${{ secrets.VERCEL_APP_URL }}
        CRON_SECRET: ${{ secrets.CRON_SECRET }}
