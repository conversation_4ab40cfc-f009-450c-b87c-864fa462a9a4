import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface SendVerificationRequestParams {
  identifier: string;
  url: string;
  expires: Date;
  provider: {
    server: string;
    from: string;
  };
  token: string;
}

export async function sendVerificationRequest({
  identifier: email,
  url,
  provider,
}: SendVerificationRequestParams): Promise<void> {
  // Clean and validate the URL
  const cleanUrl = url.trim().replace(/\s+/g, '');

  let parsedUrl;
  try {
    parsedUrl = new URL(cleanUrl);
  } catch (urlError) {
    console.error('URL parsing failed:', urlError);
    console.error('Invalid URL:', cleanUrl);
    throw new Error(`Invalid verification URL generated: ${cleanUrl}`);
  }

  const { host } = parsedUrl;

  try {
    // Use the recipient override for testing
    const recipient = process.env.RESEND_RECIPIENT || email;

    const result = await resend.emails.send({
      from: provider.from,
      to: recipient,
      subject: `Sign in to ${host}`,
      html: getEmailHtml({ url: cleanUrl, host, email }),
      text: getEmailText({ url: cleanUrl, host, email }),
    });

    if (result.error) {
      throw new Error(`Failed to send email: ${result.error.message}`);
    }
  } catch (error) {
    console.error('Error sending verification email:', error);
    throw error;
  }
}

function getEmailHtml({ url, host, email }: { url: string; host: string; email: string }) {
  const escapedEmail = `${email.replace(/\./g, "&#8203;.")}`;
  const escapedHost = `${host.replace(/\./g, "&#8203;.")}`;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign in to ${escapedHost}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    .container {
      background-color: white;
      padding: 40px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #2563eb;
      margin-bottom: 10px;
    }
    .title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #1f2937;
    }
    .button {
      display: inline-block;
      background-color: #2563eb;
      color: white;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      margin: 20px 0;
    }
    .button:hover {
      background-color: #1d4ed8;
    }
    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
      font-size: 14px;
      color: #6b7280;
    }
    .security-note {
      background-color: #f3f4f6;
      padding: 15px;
      border-radius: 6px;
      margin-top: 20px;
      font-size: 14px;
      color: #4b5563;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">Next.js Auth</div>
      <h1 class="title">Sign in to ${escapedHost}</h1>
    </div>
    
    <p>Hello,</p>
    <p>You requested to sign in to <strong>${escapedHost}</strong> using this email address: <strong>${escapedEmail}</strong></p>
    
    <div style="text-align: center;">
      <a href="${url}" class="button">Sign In</a>
    </div>

    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
    <p style="word-break: break-all; color: #2563eb;">${url}</p>

    <div style="background-color: #fef3c7; padding: 10px; border-radius: 4px; margin: 20px 0; font-size: 12px;">
      <strong>Debug Info:</strong><br>
      URL Length: ${url.length}<br>
      URL: <code style="word-break: break-all;">${url}</code>
    </div>
    
    <div class="security-note">
      <strong>Security Note:</strong> This link will expire in 24 hours and can only be used once. If you didn't request this email, you can safely ignore it.
    </div>
    
    <div class="footer">
      <p>This email was sent to ${escapedEmail} because you requested to sign in to ${escapedHost}.</p>
    </div>
  </div>
</body>
</html>
`;
}

function getEmailText({ url, host, email }: { url: string; host: string; email: string }) {
  return `
Sign in to ${host}

Hello,

You requested to sign in to ${host} using this email address: ${email}

Click the link below to sign in:
${url}

Security Note: This link will expire in 24 hours and can only be used once. If you didn't request this email, you can safely ignore it.

This email was sent to ${email} because you requested to sign in to ${host}.
`;
}
