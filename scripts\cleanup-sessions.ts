/**
 * Standalone session cleanup script (TypeScript version)
 * Can be run as a cron job or scheduled task
 * 
 * Usage:
 * npx tsx scripts/cleanup-sessions.ts
 * 
 * Or add to package.json scripts:
 * "cleanup-sessions": "npx tsx scripts/cleanup-sessions.ts"
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface CleanupResult {
  expiredSessions: number;
  expiredTokens: number;
  success: boolean;
  error?: string;
}

async function cleanupExpiredSessions(): Promise<CleanupResult> {
  try {
    console.log('Starting session cleanup...');
    const now = new Date();
    
    // Clean up expired sessions
    const expiredSessions = await prisma.session.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    // Clean up expired verification tokens
    const expiredTokens = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    const result: CleanupResult = {
      expiredSessions: expiredSessions.count,
      expiredTokens: expiredTokens.count,
      success: true
    };

    console.log('Session cleanup completed:', {
      expiredSessions: result.expiredSessions,
      expiredTokens: result.expiredTokens,
      timestamp: now.toISOString()
    });

    return result;
  } catch (error) {
    console.error('Session cleanup failed:', error);
    return {
      expiredSessions: 0,
      expiredTokens: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupExpiredSessions()
    .then((result) => {
      console.log('Cleanup result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Cleanup script failed:', error);
      process.exit(1);
    });
}

export { cleanupExpiredSessions };
