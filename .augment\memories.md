# Project Memories

## Guideline Details

### App Router
App Router is preferred because it provides better performance and more intuitive routing.

### Server Components
Server Components is preferred to reduce client-side JavaScript and improve initial load times.

## Project History
- Started with Next.js 15.3.5
- Using Google OAuth for authentication
- Fixed authentication system mismatch: middleware and dashboard pages now use the same custom session system to prevent redirect errors

## Command Preferences
- Use direct pnpm commands (e.g., "pnpm dev", "pnpm build", etc) instead of prefixing with "cmd /c"
- In this Windows PowerShell environment, avoid PowerShell-specific commands or cmdlets (Get-Process, Set-ExecutionPolicy, etc.) for development tasks due to execution policy restrictions
- Use standard cross-platform commands or documented direct pnpm commands for package management, development servers, builds, and testing
- Note: The launch-process tool has output buffering issues that make pnpm commands appear to hang when using read-process, but commands execute successfully (verify with read-terminal)

## Development Workflow

### Local Agent Workflow
1. **Pre-Task**: Create new branch from main before starting work
2. **During Task**: Complete development and testing
3. **Post-Task**: Confirm with peer before proceeding with commit and PR to allow time for local review of changes
4. **After Confirmation**: Create commit and PR for deployment

### Remote Agent Workflow
- Continue with their existing workflow (no change from current process)

## Future Reminders
- **October 2025**: Revisit Auth.js v5 to reevaluate WebAuthn/Passkey implementation options for the NextAuth.js project, as v4 lacks WebAuthn support and v5 should be stable by then

