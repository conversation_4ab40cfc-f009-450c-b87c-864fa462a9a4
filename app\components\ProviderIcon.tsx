import GoogleIcon from './icons/GoogleIcon';
import GitHubIcon from './icons/GitHubIcon';
import EmailIcon from './icons/EmailIcon';

interface ProviderIconProps {
  provider?: string;
  size?: number;
  className?: string;
}

export default function ProviderIcon({ provider, size = 20, className = "" }: ProviderIconProps) {
  if (!provider) {
    return null;
  }

  switch (provider.toLowerCase()) {
    case 'google':
      return <GoogleIcon size={size} className={className} />;
    case 'github':
      return <GitHubIcon size={size} className={className} />;
    case 'email':
      return <EmailIcon size={size} className={className} />;
    default:
      return null;
  }
}
