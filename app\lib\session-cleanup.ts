import { prisma } from './prisma';

/**
 * Session cleanup utilities
 * Handles removal of expired sessions and tokens
 */

export async function cleanupExpiredSessions() {
  try {
    const now = new Date();
    
    // Clean up expired sessions (only relevant for database session strategy)
    const expiredSessions = await prisma.session.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    // Clean up expired verification tokens
    const expiredTokens = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    console.log(`Session cleanup completed:`, {
      expiredSessions: expiredSessions.count,
      expiredTokens: expiredTokens.count,
      timestamp: now.toISOString()
    });

    return {
      expiredSessions: expiredSessions.count,
      expiredTokens: expiredTokens.count,
      success: true
    };
  } catch (error) {
    console.error('Session cleanup failed:', error);
    return {
      expiredSessions: 0,
      expiredTokens: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Clean up all sessions for a specific user
 * Useful when switching session strategies or for security purposes
 */
export async function cleanupUserSessions(userId: string) {
  try {
    const deletedSessions = await prisma.session.deleteMany({
      where: {
        userId: userId
      }
    });

    console.log(`Cleaned up ${deletedSessions.count} sessions for user ${userId}`);
    return deletedSessions.count;
  } catch (error) {
    console.error('User session cleanup failed:', error);
    return 0;
  }
}

/**
 * Clean up all database sessions (useful when switching to JWT strategy)
 */
export async function cleanupAllDatabaseSessions() {
  try {
    const deletedSessions = await prisma.session.deleteMany({});
    
    console.log(`Cleaned up all database sessions: ${deletedSessions.count} sessions removed`);
    return deletedSessions.count;
  } catch (error) {
    console.error('Database session cleanup failed:', error);
    return 0;
  }
}

/**
 * Get session statistics
 */
export async function getSessionStats() {
  try {
    const now = new Date();
    
    const totalSessions = await prisma.session.count();
    const expiredSessions = await prisma.session.count({
      where: {
        expires: {
          lt: now
        }
      }
    });
    const activeSessions = totalSessions - expiredSessions;
    
    const totalTokens = await prisma.verificationToken.count();
    const expiredTokens = await prisma.verificationToken.count({
      where: {
        expires: {
          lt: now
        }
      }
    });
    const activeTokens = totalTokens - expiredTokens;

    return {
      sessions: {
        total: totalSessions,
        active: activeSessions,
        expired: expiredSessions
      },
      tokens: {
        total: totalTokens,
        active: activeTokens,
        expired: expiredTokens
      },
      timestamp: now.toISOString()
    };
  } catch (error) {
    console.error('Failed to get session stats:', error);
    return null;
  }
}
