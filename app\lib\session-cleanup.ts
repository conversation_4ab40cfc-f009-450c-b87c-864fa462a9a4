import { prisma, ensureDatabaseReady } from './prisma';

/**
 * Session cleanup utilities
 *
 * IMPORTANT: This project uses JWT session strategy, so database sessions are NOT used.
 * The Session table cleanup is included for future compatibility if switching to database sessions.
 *
 * Currently active cleanup:
 * - VerificationToken: Used for email magic links (always relevant)
 *
 * Currently inactive cleanup (JWT strategy):
 * - Session: Would be used for database session strategy (currently unused)
 */

export async function cleanupExpiredSessions() {
  try {
    // Ensure database is properly initialized before cleanup
    await ensureDatabaseReady();

    const now = new Date();
    let expiredSessionsCount = 0;

    // Clean up expired sessions (only relevant for database session strategy)
    // NOTE: Currently using JWT strategy - this cleanup is inactive but kept for future compatibility
    // Skip session cleanup if using JWT strategy or if Session table doesn't exist
    try {
      const expiredSessions = await prisma.session.deleteMany({
        where: {
          expires: {
            lt: now
          }
        }
      });
      expiredSessionsCount = expiredSessions.count;
    } catch (sessionError: unknown) {
      // If Session table doesn't exist (P2021) or other session-related errors, log and continue
      const error = sessionError as { code?: string; message?: string };
      if (error?.code === 'P2021' || error?.message?.includes('Session')) {
        console.log('Session table not available - this is expected with JWT strategy (sessions stored in tokens, not database)');
        expiredSessionsCount = 0;
      } else {
        // Re-throw other unexpected errors
        throw sessionError;
      }
    }

    // Clean up expired verification tokens (always relevant for email magic links regardless of session strategy)
    const expiredTokens = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    console.log(`Session cleanup completed:`, {
      expiredSessions: expiredSessionsCount,
      expiredTokens: expiredTokens.count,
      timestamp: now.toISOString(),
      note: expiredSessionsCount === 0 ? 'JWT strategy active - sessions stored in tokens, not database' : undefined
    });

    return {
      expiredSessions: expiredSessionsCount,
      expiredTokens: expiredTokens.count,
      success: true
    };
  } catch (error) {
    console.error('Session cleanup failed:', error);
    return {
      expiredSessions: 0,
      expiredTokens: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Clean up all sessions for a specific user
 * NOTE: Currently inactive with JWT strategy (sessions stored in tokens, not database)
 * Useful when switching session strategies or for security purposes
 */
export async function cleanupUserSessions(userId: string) {
  try {
    const deletedSessions = await prisma.session.deleteMany({
      where: {
        userId: userId
      }
    });

    console.log(`Cleaned up ${deletedSessions.count} sessions for user ${userId}`);
    return deletedSessions.count;
  } catch (error) {
    console.error('User session cleanup failed:', error);
    return 0;
  }
}

/**
 * Clean up all database sessions (useful when switching to JWT strategy)
 * NOTE: Currently inactive with JWT strategy - would be used to clear sessions when migrating from database to JWT
 */
export async function cleanupAllDatabaseSessions() {
  try {
    const deletedSessions = await prisma.session.deleteMany({});
    
    console.log(`Cleaned up all database sessions: ${deletedSessions.count} sessions removed`);
    return deletedSessions.count;
  } catch (error) {
    console.error('Database session cleanup failed:', error);
    return 0;
  }
}

/**
 * Get session statistics
 */
export async function getSessionStats() {
  try {
    const now = new Date();
    let sessionStats = {
      total: 0,
      active: 0,
      expired: 0
    };

    // Try to get session stats, but handle JWT strategy gracefully
    try {
      const totalSessions = await prisma.session.count();
      const expiredSessions = await prisma.session.count({
        where: {
          expires: {
            lt: now
          }
        }
      });
      const activeSessions = totalSessions - expiredSessions;

      sessionStats = {
        total: totalSessions,
        active: activeSessions,
        expired: expiredSessions
      };
    } catch (sessionError: unknown) {
      // If Session table doesn't exist (P2021), use default values
      const error = sessionError as { code?: string; message?: string };
      if (error?.code === 'P2021' || error?.message?.includes('Session')) {
        console.log('Session table not available - expected with JWT strategy (sessions in tokens, not database)');
        sessionStats = {
          total: 0,
          active: 0,
          expired: 0
        };
      } else {
        throw sessionError;
      }
    }

    const totalTokens = await prisma.verificationToken.count();
    const expiredTokens = await prisma.verificationToken.count({
      where: {
        expires: {
          lt: now
        }
      }
    });
    const activeTokens = totalTokens - expiredTokens;

    return {
      sessions: sessionStats,
      tokens: {
        total: totalTokens,
        active: activeTokens,
        expired: expiredTokens
      },
      timestamp: now.toISOString(),
      note: sessionStats.total === 0 ? 'JWT strategy active - sessions stored in tokens, not database' : undefined
    };
  } catch (error) {
    console.error('Failed to get session stats:', error);
    return null;
  }
}
