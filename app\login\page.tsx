'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import GoogleSignIn from '@/app/components/GoogleSignIn';
import GitHubSignIn from '@/app/components/GitHubSignIn';
import EmailSignIn from '@/app/components/EmailSignIn';
import { AuthStateProvider } from '@/app/contexts/AuthStateContext';
import Link from 'next/link';

export default function LoginPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Fallback redirect if NextAuth redirect callback fails
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Use replace to avoid adding to history
      router.replace('/dashboard');
    }
  }, [status, session, router]);

  // Show loading state for any non-unauthenticated status
  if (status !== 'unauthenticated') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {status === 'loading' ? 'Loading...' : 'Redirecting to dashboard...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 sm:px-6 lg:px-8 relative flex flex-col justify-center">
      {/* Responsive layout container with conditional classes */}
      <div className="flex flex-col items-center justify-center w-full">
          {/* Header section */}
          <div className="text-center max-w-md w-full mb-8 lg:mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900 mb-2">
              Sign In to Your Account
            </h2>
            <p className="text-sm sm:text-base text-gray-600 mb-3">
              Choose your preferred sign-in method
            </p>
          </div>

          {/* Authentication options */}
          <AuthStateProvider>
            <div className="max-w-md w-full mb-8 lg:mb-12 space-y-6">
              {/* OAuth providers */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <GoogleSignIn />
                <GitHubSignIn />
              </div>

              {/* Separator */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-gray-50 text-gray-500">or</span>
                </div>
              </div>

              {/* Email sign-in */}
              <EmailSignIn />
            </div>
          </AuthStateProvider>

        {/* Footer section */}
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
            aria-label="Return to home page"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
